import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Meal Queries
export const getMeals = query({
  args: {
    date: v.optional(v.string()),
    type: v.optional(
      v.union(
        v.literal("breakfast"),
        v.literal("lunch"),
        v.literal("dinner"),
        v.literal("snacks")
      )
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("meal")
      .withIndex("by_user_date", (q) => q.eq("userId", identity.subject))
      .filter((q) => q.eq(q.field("isDeleted"), false));

    if (args.date) {
      query = query.filter((q) => q.eq(q.field("date"), args.date));
    }

    if (args.type) {
      query = query.filter((q) => q.eq(q.field("type"), args.type));
    }

    return await query.collect();
  },
});

export const getMealById = query({
  args: { id: v.id("meal") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const meal = await ctx.db.get(args.id);
    if (!meal) {
      throw new Error("Meal not found");
    }

    if (meal.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    return meal;
  },
});

// Meal Mutations
export const createMeal = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    calories: v.number(),
    protein: v.number(),
    carbs: v.number(),
    fat: v.number(),
    // Extended nutrition fields
    fiber: v.optional(v.number()),
    sugar: v.optional(v.number()),
    sodium: v.optional(v.number()),
    water: v.optional(v.number()),
    calcium: v.optional(v.number()),
    iron: v.optional(v.number()),
    vitaminC: v.optional(v.number()),
    vitaminD: v.optional(v.number()),
    type: v.union(
      v.literal("breakfast"),
      v.literal("lunch"),
      v.literal("dinner"),
      v.literal("snacks")
    ),
    date: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const mealId = await ctx.db.insert("meal", {
      ...args,
      userId: identity.subject,
      isDeleted: false,
    });

    return mealId;
  },
});

export const updateMeal = mutation({
  args: {
    id: v.id("meal"),
    updates: v.object({
      name: v.optional(v.string()),
      description: v.optional(v.string()),
      calories: v.optional(v.number()),
      protein: v.optional(v.number()),
      carbs: v.optional(v.number()),
      fat: v.optional(v.number()),
      // Extended nutrition fields
      fiber: v.optional(v.number()),
      sugar: v.optional(v.number()),
      sodium: v.optional(v.number()),
      water: v.optional(v.number()),
      calcium: v.optional(v.number()),
      iron: v.optional(v.number()),
      vitaminC: v.optional(v.number()),
      vitaminD: v.optional(v.number()),
      type: v.optional(
        v.union(
          v.literal("breakfast"),
          v.literal("lunch"),
          v.literal("dinner"),
          v.literal("snacks")
        )
      ),
      date: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const meal = await ctx.db.get(args.id);
    if (!meal) {
      throw new Error("Meal not found");
    }

    if (meal.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const deleteMeal = mutation({
  args: { id: v.id("meal") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const meal = await ctx.db.get(args.id);
    if (!meal) {
      throw new Error("Meal not found");
    }

    if (meal.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    // Soft delete
    await ctx.db.patch(args.id, { isDeleted: true });
    return { success: true };
  },
});

// Get meals summary for a date range
export const getMealsSummary = query({
  args: {
    startDate: v.string(),
    endDate: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const meals = await ctx.db
      .query("meal")
      .withIndex("by_user_date", (q) => q.eq("userId", identity.subject))
      .filter((q) =>
        q.and(
          q.eq(q.field("isDeleted"), false),
          q.gte(q.field("date"), args.startDate),
          q.lte(q.field("date"), args.endDate)
        )
      )
      .collect();

    // Calculate summary
    const summary = {
      totalCalories: 0,
      totalProtein: 0,
      totalCarbs: 0,
      totalFat: 0,
      totalFiber: 0,
      totalSugar: 0,
      totalSodium: 0,
      totalWater: 0,
      totalCalcium: 0,
      totalIron: 0,
      totalVitaminC: 0,
      totalVitaminD: 0,
      mealsByType: {
        breakfast: 0,
        lunch: 0,
        dinner: 0,
        snacks: 0,
      },
    };

    meals.forEach((meal) => {
      summary.totalCalories += meal.calories;
      summary.totalProtein += meal.protein;
      summary.totalCarbs += meal.carbs;
      summary.totalFat += meal.fat;
      summary.totalFiber += meal.fiber || 0;
      summary.totalSugar += meal.sugar || 0;
      summary.totalSodium += meal.sodium || 0;
      summary.totalWater += meal.water || 0;
      summary.totalCalcium += meal.calcium || 0;
      summary.totalIron += meal.iron || 0;
      summary.totalVitaminC += meal.vitaminC || 0;
      summary.totalVitaminD += meal.vitaminD || 0;
      summary.mealsByType[meal.type]++;
    });

    // Also include drinks in the nutrition summary
    const drinks = await ctx.db
      .query("drink")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .filter((q) =>
        q.and(
          q.eq(q.field("isDeleted"), false),
          q.gte(q.field("date"), args.startDate),
          q.lte(q.field("date"), args.endDate)
        )
      )
      .collect();

    drinks.forEach((drink) => {
      summary.totalCalories += drink.calories;
      summary.totalProtein += drink.protein;
      summary.totalCarbs += drink.carbs;
      summary.totalFat += drink.fat;
      summary.totalFiber += drink.fiber || 0;
      summary.totalSugar += drink.sugar || 0;
      summary.totalSodium += drink.sodium || 0;
      summary.totalWater += drink.water || 0;
      summary.totalCalcium += drink.calcium || 0;
      summary.totalIron += drink.iron || 0;
      summary.totalVitaminC += drink.vitaminC || 0;
      summary.totalVitaminD += drink.vitaminD || 0;
    });

    return summary;
  },
});

export const createMealWithImage = mutation({
  args: {
    imageUrl: v.string(),
    type: v.union(
      v.literal("breakfast"),
      v.literal("lunch"),
      v.literal("dinner"),
      v.literal("snacks")
    ),
    date: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // // Get the active health plan
    // const activePlan = await ctx.db
    //   .query('activeHealthPlan')
    //   .withIndex('by_user', (q) => q.eq('userId', identity.subject))
    //   .filter((q) =>
    //     q.and(
    //       q.eq(q.field('isDeleted'), false),
    //       q.lte(q.field('startDate'), args.date),
    //       q.or(
    //         q.eq(q.field('endDate'), undefined),
    //         q.gte(q.field('endDate'), args.date)
    //       )
    //     )
    //   )
    //   .first();

    // // Analyze the image using AI
    // const analysis = await ctx.runAction(internal.meals.analyzeMealImage, {
    //   imageUrl: args.imageUrl,
    // });

    // // Create the meal record
    // const mealId = await ctx.db.insert('meal', {
    //   userId: identity.subject,
    //   name: analysis.description || 'Unknown meal',
    //   description: analysis.description || '',
    //   calories: analysis.calories || 0,
    //   protein: analysis.protein || 0,
    //   carbs: analysis.carbs || 0,
    //   fat: analysis.fat || 0,
    //   type: args.type,
    //   date: args.date,
    //   imageUrl: args.imageUrl,
    //   healthPlanId: activePlan?.planId,
    //   isDeleted: false,
    // });

    // For now, return a dummy analysis. In a real app, this would come from an AI service.
    const dummyAnalysis = {
      calories: 500,
      protein: 20,
      carbs: 50,
      fat: 15,
      fiber: 8,
      sugar: 12,
      sodium: 400,
      water: 200,
      calcium: 150,
      iron: 3,
      vitaminC: 25,
      vitaminD: 50,
      description: "Analyzed meal (placeholder)",
    };

    // Create the meal record
    const mealId = await ctx.db.insert("meal", {
      userId: identity.subject,
      name: dummyAnalysis.description,
      description: dummyAnalysis.description,
      calories: dummyAnalysis.calories,
      protein: dummyAnalysis.protein,
      carbs: dummyAnalysis.carbs,
      fat: dummyAnalysis.fat,
      fiber: dummyAnalysis.fiber,
      sugar: dummyAnalysis.sugar,
      sodium: dummyAnalysis.sodium,
      water: dummyAnalysis.water,
      calcium: dummyAnalysis.calcium,
      iron: dummyAnalysis.iron,
      vitaminC: dummyAnalysis.vitaminC,
      vitaminD: dummyAnalysis.vitaminD,
      type: args.type,
      date: args.date,
      imageUrl: args.imageUrl,
      // healthPlanId: activePlan?.planId, // Uncomment if activePlan is used
      isDeleted: false,
    });

    return { mealId, analysis: dummyAnalysis };
  },
});

export const getTotalCaloriesToday = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const today = new Date().toISOString().split("T")[0]; // Get YYYY-MM-DD

    const meals = await ctx.db
      .query("meal")
      .withIndex("by_user_date", (q) => q.eq("userId", identity.subject))
      .filter((q) =>
        q.and(q.eq(q.field("isDeleted"), false), q.eq(q.field("date"), today))
      )
      .collect();

    const caloriesByMealType = {
      breakfast: 0,
      lunch: 0,
      dinner: 0,
      snacks: 0,
    };

    meals.forEach((meal) => {
      if (meal.type in caloriesByMealType) {
        caloriesByMealType[meal.type] += meal.calories;
      }
    });

    return caloriesByMealType;
  },
});
